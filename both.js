const puppeteer = require('puppeteer-extra'),
  StealthPlugin = require('puppeteer-extra-plugin-stealth'),
  axios = require('axios'),
  fs = require('fs').promises,
  path = require('path'),
  config = require('./config2.json'),
  RssParser = require('rss-parser'),
  cheerio = require('cheerio')
puppeteer.use(StealthPlugin())

const USED_PEXELS_FILE = path.join(__dirname, 'used_images.txt'),
  USED_RSS_LINKS_FILE = path.join(__dirname, 'posted_rss_links.txt'),
  COOKIES_PATH = path.join(__dirname, 'cookies.json'),
  LAST_KEYWORD_INDEX_FILE = path.join(__dirname, 'last_keyword_index.txt')
async function getUsedItems(filePath) {
  try {
    const fileContent = await fs.readFile(filePath, 'utf8')
    return new Set(
      fileContent.split('\n').filter((line) => line.trim() !== '')
    )
  } catch (error) {
    if (error.code === 'ENOENT') {
      return new Set()
    }
    throw error
  }
}
async function addUsedItem(filePath, item) {
  await fs.appendFile(filePath, item + '\n')
}
async function getPexelsImage(keyword, usedImages) {
  console.log('Mencari gambar di Pexels dengan keyword: "' + keyword + '"')
  const pexelsResponse = await axios.get(
      'https://api.pexels.com/v1/search?query=' +
        encodeURIComponent(keyword) +
        '&per_page=20',
      { headers: { Authorization: process.env.PEXELS_API_KEY } }
    ),
    photos = pexelsResponse.data.photos
  if (!photos || photos.length === 0) {
    throw new Error(
      'Tidak ada gambar Pexels yang ditemukan untuk keyword: ' + keyword + '.'
    )
  }
  const availablePhoto = photos.find(
    (photo) => !usedImages.has(String(photo.id))
  )
  if (!availablePhoto) {
    throw new Error(
      'Semua gambar Pexels yang ditemukan sudah pernah digunakan.'
    )
  }
  return (
    console.log('Gambar Pexels ditemukan: ' + availablePhoto.url),
    {
      url: availablePhoto.src.large,
      id: String(availablePhoto.id),
    }
  )
}
async function getGeminiContentForPexels(keyword) {
  console.log('Menghubungi Gemini AI untuk konten...')
  const prompt = config.gemini.prompt.replace('{keyword}', keyword)
  try {
    const geminiResponse = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: prompt }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      generatedText = geminiResponse.data.candidates[0].content.parts[0].text,
      titleMatch = generatedText.match(/Judul: (.*)/),
      descriptionMatch = generatedText.match(/Deskripsi: (.*)/s)
    if (titleMatch && descriptionMatch) {
      return (
        console.log('Konten dari Gemini berhasil didapatkan.'),
        {
          title: titleMatch[1].trim(),
          description: descriptionMatch[1].trim(),
        }
      )
    }
  } catch (error) {
    console.error(
      'Error saat menghubungi Gemini AI:',
      error.response
        ? error.response.data.error.message
        : error.message
    )
  }
  return (
    console.log('Menggunakan konten fallback.'),
    {
      title: config.gemini.fallbackTitle,
      description: config.gemini.fallbackDescription.replace(
        '{keyword}',
        '#' + keyword.replace(/\s+/g, '')
      ),
    }
  )
}
async function getGeminiContentForRss(articleTitle) {
  console.log('Menghubungi Gemini AI untuk konten RSS...')
  const rssPrompt = config.gemini.rssPrompt.replace('{articleTitle}', articleTitle)
  try {
    const geminiResponse = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: rssPrompt }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      generatedContent = geminiResponse.data.candidates[0].content.parts[0].text
    if (config.rss.descriptionMode === 'AI_WITH_TITLE') {
      return '**' + articleTitle + '**\n\n' + generatedContent
    }
    return generatedContent
  } catch (error) {
    console.error(
      'Error saat menghubungi Gemini AI untuk RSS:',
      error.response
        ? error.response.data.error.message
        : error.message
    )
  }
  return console.log('Menggunakan deskripsi fallback dari artikel.'), articleTitle
}
async function findImageInRssEntry(rssEntry) {
  if (rssEntry.enclosure && rssEntry.enclosure.url) {
    return rssEntry.enclosure.url
  }
  if (rssEntry['media:content'] && rssEntry['media:content']['$'].url) {
    return rssEntry['media:content']['$'].url
  }
  if (rssEntry.content) {
    const $ = cheerio.load(rssEntry.content),
      imageUrl = $('img').first().attr('src')
    if (imageUrl) {
      return imageUrl
    }
  }
  return null
}
async function getGeminiTags(pinTitle) {
  console.log('Menghubungi Gemini AI untuk kata pancingan tags...')
  const tagsPrompt = config.gemini.tagsPrompt.replace('{pinTitle}', pinTitle)
  try {
    const geminiResponse = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: tagsPrompt }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      generatedTags = geminiResponse.data.candidates[0].content.parts[0].text
    return generatedTags
      .split(',')
      .map((tag) => tag.trim())
      .filter((tag) => tag)
  } catch (error) {
    return (
      console.error(
        'Error saat membuat kata pancingan tags:',
        error.response
          ? error.response.data.error.message
          : error.message
      ),
      []
    )
  }
}
async function createPinFromPexels(page) {
  console.log('\n--- \uD83D\uDE80 Starting Pexels Mode ---')
  const keywordFilePath = path.join(
    __dirname,
    config.pexels.keywordFile || 'keywords.txt'
  )
  let keywords = []
  try {
    const keywordFileContent = await fs.readFile(keywordFilePath, 'utf8')
    keywords = keywordFileContent
      .split('\n')
      .filter((line) => line.trim() !== '')
    if (keywords.length === 0) {
      throw new Error("File keyword '" + keywordFilePath + "' kosong.")
    }
  } catch (error) {
    throw new Error(
      "Gagal membaca file keyword '" + keywordFilePath + "': " + error.message
    )
  }
  let lastKeywordIndex = -1
  try {
    const indexContent = await fs.readFile(LAST_KEYWORD_INDEX_FILE, 'utf8')
    lastKeywordIndex = parseInt(indexContent.trim(), 10)
  } catch (error) {
    console.log(
      'File last_keyword_index.txt tidak ditemukan, memulai dari awal.'
    )
  }
  const currentKeywordIndex = (lastKeywordIndex + 1) % keywords.length,
    currentKeyword = keywords[currentKeywordIndex]
  console.log(
    'Menggunakan keyword dari file (baris ' +
      (currentKeywordIndex + 1) +
      '): "' +
      currentKeyword +
      '"'
  )
  const usedImages = await getUsedItems(USED_PEXELS_FILE),
    selectedImage = await getPexelsImage(currentKeyword, usedImages),
    generatedContent = await getGeminiContentForPexels(currentKeyword),
    destinationLink = config.settings.destinationLink
  await postToPinterest(page, {
    title: generatedContent.title,
    description: generatedContent.description,
    imageUrl: selectedImage.url,
    destinationLink: destinationLink,
  })
  await addUsedItem(USED_PEXELS_FILE, selectedImage.id)
  await fs.writeFile(LAST_KEYWORD_INDEX_FILE, String(currentKeywordIndex), 'utf8')
  console.log('--- \u2705 Pexels Mode Completed ---')
}
async function createPinFromRss(page) {
  console.log('\n--- \uD83D\uDE80 Starting RSS Mode ---')
  const rssParser = new RssParser({
      customFields: { item: ['media:content'] },
    }),
    rssFeed = await rssParser.parseURL(config.rss.feedUrl),
    usedRssLinks = await getUsedItems(USED_RSS_LINKS_FILE),
    startFromLink = config.rss.startFromLink
  let availableItems = []
  const allLinks = rssFeed.items.map((item) => item.link),
    startIndex = allLinks.indexOf(startFromLink)
  startFromLink && startIndex !== -1
    ? (availableItems = rssFeed.items.slice(0, startIndex))
    : (availableItems = rssFeed.items)
  const newArticles = availableItems
    .filter((article) => !usedRssLinks.has(article.link))
    .sort(
      (a, b) =>
        new Date(a.pubDate) - new Date(b.pubDate)
    )
  if (newArticles.length === 0) {
    console.log('No new articles in RSS feed.')
    return
  }
  const selectedArticle = newArticles[0]
  console.log('Preparing article: "' + selectedArticle.title + '"')
  let imageUrl = await findImageInRssEntry(selectedArticle),
    pexelsImageId = null
  if (!imageUrl) {
    const titleWords = selectedArticle.title.split(' '),
      keyword =
        titleWords.find((word) => word.length > 4) || titleWords[0],
      usedPexelsImages = await getUsedItems(USED_PEXELS_FILE),
      fallbackImage = await getPexelsImage(keyword, usedPexelsImages)
    imageUrl = fallbackImage.url
    pexelsImageId = fallbackImage.id
  }
  const generatedDescription = await getGeminiContentForRss(selectedArticle.title)
  await postToPinterest(page, {
    title: selectedArticle.title,
    description: generatedDescription,
    imageUrl: imageUrl,
    destinationLink: selectedArticle.link,
  })
  await addUsedItem(USED_RSS_LINKS_FILE, selectedArticle.link)
  pexelsImageId && (await addUsedItem(USED_PEXELS_FILE, pexelsImageId))
  console.log('--- \u2705 RSS Mode Completed ---')
}
async function postToPinterest(page, pinData) {
  const tempImagePath = path.join(__dirname, 'temp_image.jpg')
  try {
    console.log('Preparing to post pin: "' + pinData.title + '"')
    await page.goto(config.pinterest.boardUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 90000,
    })
    console.log('Looking for "Create" (+) button...')
    await page.waitForSelector(
      'button[aria-label="Create a Pin or add a Section"]',
      {
        visible: true,
        timeout: 30000,
      }
    )
    await page.click('button[aria-label="Create a Pin or add a Section"]')
    console.log('Looking for "Pin" menu...')
    await page.waitForSelector('button[data-test-id="Create Story Pin"]', {
      visible: true,
      timeout: 10000,
    })
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'domcontentloaded' }),
      page.click('button[data-test-id="Create Story Pin"]'),
    ])
    console.log('Successfully entered Pin Builder. Uploading image...')
    const imageResponse = await axios.get(pinData.imageUrl, {
      responseType: 'arraybuffer',
      headers: {
        Referer: pinData.destinationLink || config.pinterest.boardUrl,
      },
    })
    await fs.writeFile(tempImagePath, imageResponse.data)
    const fileInput = await page.waitForSelector('input[type=file]', {
      timeout: 60000,
    })
    await fileInput.uploadFile(tempImagePath)
    console.log('Filling Pin details...')
    const descriptionSelector =
        'div[data-test-id="editor-with-mentions"] [contenteditable="true"], div[data-test-id="pin-draft-description"] [contenteditable="true"]',
      websiteSelector = 'input#WebsiteField, textarea[data-test-id="pin-draft-link"]',
      tagsSelector = 'input#storyboard-selector-interest-tags',
      publishSelector =
        'div[data-test-id="storyboard-creation-nav-done"] button, button[data-test-id="board-dropdown-save-button"]'
    await page.waitForSelector(
      'input#storyboard-selector-title, textarea[data-test-id="pin-draft-title"]',
      { timeout: 60000 }
    )
    await page.type(
      'input#storyboard-selector-title, textarea[data-test-id="pin-draft-title"]',
      pinData.title,
      { delay: 150 }
    )
    await page.type(descriptionSelector, pinData.description, { delay: 150 })
    pinData.destinationLink &&
      (await page.type(websiteSelector, pinData.destinationLink, {
        delay: 150,
      }))
    const maxTagsCount = config.tags ? config.tags.count || 0 : 0
    if (maxTagsCount > 0) {
      try {
        const tagsInput = await page.waitForSelector(tagsSelector, {
          timeout: 5000,
        })
        let addedTagsCount = 0
        const suggestedTags = await getGeminiTags(pinData.title),
          suggestionSelector = 'div[data-test-id="storyboard-suggestions-item"]'
        for (const tag of suggestedTags) {
          if (addedTagsCount >= maxTagsCount) {
            break
          }
          try {
            await tagsInput.focus()
            await page.type(tagsSelector, tag, { delay: 150 })
            await page.waitForTimeout(2000)
            await page.waitForSelector(suggestionSelector, {
              visible: true,
              timeout: 5000,
            })
            await page.click(suggestionSelector)
            addedTagsCount++
          } catch (error) {
            await tagsInput.focus()
            await page.keyboard.down('Control')
            await page.keyboard.press('A')
            await page.keyboard.up('Control')
            await page.keyboard.press('Backspace')
          }
        }
        console.log(
          'Total ' +
            addedTagsCount +
            ' of ' +
            maxTagsCount +
            ' tags successfully added.'
        )
      } catch (error) {
        console.log(
          'Tag input field not found, skipping tagging step.'
        )
      }
    }
    console.log('Publishing/Saving Pin...')
    await page.waitForSelector(publishSelector, { visible: true })
    await page.click(publishSelector)
    await page.waitForTimeout(15000)
    try {
      const remainingPublishButton = await page.waitForSelector(publishSelector, {
        timeout: 1000,
        visible: true,
      })
      remainingPublishButton &&
        (console.log(
          '"Publish/Save" button still visible, trying to click once more...'
        ),
        await remainingPublishButton.click(),
        await page.waitForTimeout(10000))
    } catch (error) {
      console.log('"Publish/Save" button has disappeared, assuming success.')
    }
    console.log('\u2705 Pin "' + pinData.title + '" successfully processed.')
  } finally {
    await fs.unlink(tempImagePath).catch(() => {})
  }
}
async function main() {
  let browser
  try {
    if (process.env.PINTEREST_COOKIES) {
      await fs.writeFile(COOKIES_PATH, process.env.PINTEREST_COOKIES, 'utf8')
    } else {
      throw new Error("Secret 'PINTEREST_COOKIES' not found.")
    }
    const cookiesContent = await fs.readFile(COOKIES_PATH, 'utf8')
    let cookies = JSON.parse(cookiesContent)
    const cleanedCookies = cookies.map((cookie) => {
      cookie.sameSite &&
        !['Lax', 'Strict', 'None'].includes(cookie.sameSite) &&
        delete cookie.sameSite
      for (const key in cookie) {
        cookie[key] === null && delete cookie[key]
      }
      return cookie
    })
    browser = await puppeteer.launch({
      executablePath: '/usr/bin/google-chrome',
      headless: 'new',
      args: ['--no-sandbox'],
    })
    const page = await browser.newPage()
    await page.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
    )
    await page.setCookie(...cleanedCookies)
    console.log('Cookies successfully set to browser.')
    ;(config.runMode === 'pexels' || config.runMode === 'both') &&
      (await createPinFromPexels(page))
    ;(config.runMode === 'rss' || config.runMode === 'both') &&
      (await createPinFromRss(page))
    console.log('\uD83C\uDF89 All tasks completed.')
  } catch (error) {
    console.error('Fatal error occurred:', error.message)
    if (browser) {
      const currentPage = (await browser.pages())[0],
        errorScreenshotPath = path.join(__dirname, 'error_screenshot.png')
      await currentPage.screenshot({
        path: errorScreenshotPath,
        fullPage: true,
      })
      console.log('Error screenshot saved at: ' + errorScreenshotPath)
    }
    process.exit(1)
  } finally {
    browser && (await browser.close())
  }
}
main()
