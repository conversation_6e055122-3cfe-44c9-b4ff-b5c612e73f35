const puppeteer = require('puppeteer-extra'),
  StealthPlugin = require('puppeteer-extra-plugin-stealth'),
  axios = require('axios'),
  fs = require('fs').promises,
  path = require('path'),
  config = require('./config2.json'),
  fetch = require('node-fetch'),
  RssParser = require('rss-parser'),
  cheerio = require('cheerio')
puppeteer.use(StealthPlugin())
const VALIDATION_SERVER_URL =
    'https://license-server-botnews.vercel.app/api/validate',
  PRODUCT_NAME = 'pinterest'
async function checkLicense() {
  console.log('Memulai validasi lisensi...')
  const _0x22d1af = process.env.BOT_LICENSE_EMAIL
  if (!_0x22d1af) {
    throw new Error(
      "KESALAHAN: GitHub Secret 'BOT_LICENSE_EMAIL' tidak ditemukan."
    )
  }
  const _0x47a86e =
    VALIDATION_SERVER_URL +
    '?email=' +
    encodeURIComponent(_0x22d1af) +
    '&product=' +
    PRODUCT_NAME
  try {
    const _0x3541c3 = await fetch(_0x47a86e),
      _0x8d6a9c = await _0x3541c3.json()
    if (_0x3541c3.ok && _0x8d6a9c.status === 'valid') {
      return console.log('Lisensi valid.'), true
    }
    throw new Error(
      'Lisensi tidak valid (Status: ' +
        _0x3541c3.status +
        ') - ' +
        (_0x8d6a9c.message || 'Tidak ada pesan')
    )
  } catch (_0x5ee9cc) {
    throw new Error('Gagal menghubungi server lisensi: ' + _0x5ee9cc.message)
  }
}
const USED_PEXELS_FILE = path.join(__dirname, 'used_images.txt'),
  USED_RSS_LINKS_FILE = path.join(__dirname, 'posted_rss_links.txt'),
  COOKIES_PATH = path.join(__dirname, 'cookies.json'),
  LAST_KEYWORD_INDEX_FILE = path.join(__dirname, 'last_keyword_index.txt')
async function getUsedItems(_0x37b498) {
  try {
    const _0x2fe9f7 = await fs.readFile(_0x37b498, 'utf8')
    return new Set(
      _0x2fe9f7.split('\n').filter((_0x2844d8) => _0x2844d8.trim() !== '')
    )
  } catch (_0x1348e9) {
    if (_0x1348e9.code === 'ENOENT') {
      return new Set()
    }
    throw _0x1348e9
  }
}
async function addUsedItem(_0x43a90f, _0x1399b8) {
  await fs.appendFile(_0x43a90f, _0x1399b8 + '\n')
}
async function getPexelsImage(_0x192de1, _0x2ca41b) {
  console.log('Mencari gambar di Pexels dengan keyword: "' + _0x192de1 + '"')
  const _0x253388 = await axios.get(
      'https://api.pexels.com/v1/search?query=' +
        encodeURIComponent(_0x192de1) +
        '&per_page=20',
      { headers: { Authorization: process.env.PEXELS_API_KEY } }
    ),
    _0x115998 = _0x253388.data.photos
  if (!_0x115998 || _0x115998.length === 0) {
    throw new Error(
      'Tidak ada gambar Pexels yang ditemukan untuk keyword: ' + _0x192de1 + '.'
    )
  }
  const _0x5e5cec = _0x115998.find(
    (_0x48c7ee) => !_0x2ca41b.has(String(_0x48c7ee.id))
  )
  if (!_0x5e5cec) {
    throw new Error(
      'Semua gambar Pexels yang ditemukan sudah pernah digunakan.'
    )
  }
  return (
    console.log('Gambar Pexels ditemukan: ' + _0x5e5cec.url),
    {
      url: _0x5e5cec.src.large,
      id: String(_0x5e5cec.id),
    }
  )
}
async function getGeminiContentForPexels(_0x3e760e) {
  console.log('Menghubungi Gemini AI untuk konten...')
  const _0x3ff30d = config.gemini.prompt.replace('{keyword}', _0x3e760e)
  try {
    const _0x4b0cf2 = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: _0x3ff30d }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      _0x208a1c = _0x4b0cf2.data.candidates[0].content.parts[0].text,
      _0x42b795 = _0x208a1c.match(/Judul: (.*)/),
      _0x1bd2ba = _0x208a1c.match(/Deskripsi: (.*)/s)
    if (_0x42b795 && _0x1bd2ba) {
      return (
        console.log('Konten dari Gemini berhasil didapatkan.'),
        {
          title: _0x42b795[1].trim(),
          description: _0x1bd2ba[1].trim(),
        }
      )
    }
  } catch (_0x3d88eb) {
    console.error(
      'Error saat menghubungi Gemini AI:',
      _0x3d88eb.response
        ? _0x3d88eb.response.data.error.message
        : _0x3d88eb.message
    )
  }
  return (
    console.log('Menggunakan konten fallback.'),
    {
      title: config.gemini.fallbackTitle,
      description: config.gemini.fallbackDescription.replace(
        '{keyword}',
        '#' + _0x3e760e.replace(/\s+/g, '')
      ),
    }
  )
}
async function getGeminiContentForRss(_0x48c5cc) {
  console.log('Menghubungi Gemini AI untuk konten RSS...')
  const _0x1079b7 = config.gemini.rssPrompt.replace('{articleTitle}', _0x48c5cc)
  try {
    const _0x7caaaf = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: _0x1079b7 }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      _0x430d69 = _0x7caaaf.data.candidates[0].content.parts[0].text
    if (config.rss.descriptionMode === 'AI_WITH_TITLE') {
      return '**' + _0x48c5cc + '**\n\n' + _0x430d69
    }
    return _0x430d69
  } catch (_0x1030f4) {
    console.error(
      'Error saat menghubungi Gemini AI untuk RSS:',
      _0x1030f4.response
        ? _0x1030f4.response.data.error.message
        : _0x1030f4.message
    )
  }
  return console.log('Menggunakan deskripsi fallback dari artikel.'), _0x48c5cc
}
async function findImageInRssEntry(_0x331651) {
  if (_0x331651.enclosure && _0x331651.enclosure.url) {
    return _0x331651.enclosure.url
  }
  if (_0x331651['media:content'] && _0x331651['media:content']['$'].url) {
    return _0x331651['media:content']['$'].url
  }
  if (_0x331651.content) {
    const _0x4541d = cheerio.load(_0x331651.content),
      _0x1721bc = _0x4541d('img').first().attr('src')
    if (_0x1721bc) {
      return _0x1721bc
    }
  }
  return null
}
async function getGeminiTags(_0x13edf8) {
  console.log('Menghubungi Gemini AI untuk kata pancingan tags...')
  const _0x5f2f6f = config.gemini.tagsPrompt.replace('{pinTitle}', _0x13edf8)
  try {
    const _0x583136 = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: _0x5f2f6f }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      _0x4cfc93 = _0x583136.data.candidates[0].content.parts[0].text
    return _0x4cfc93
      .split(',')
      .map((_0x404f40) => _0x404f40.trim())
      .filter((_0x2360d1) => _0x2360d1)
  } catch (_0x3e0303) {
    return (
      console.error(
        'Error saat membuat kata pancingan tags:',
        _0x3e0303.response
          ? _0x3e0303.response.data.error.message
          : _0x3e0303.message
      ),
      []
    )
  }
}
async function createPinFromPexels(_0x1ffc5d) {
  console.log('\n--- \uD83D\uDE80 Memulai Mode Pexels ---')
  const _0x59e6f3 = path.join(
    __dirname,
    config.pexels.keywordFile || 'keywords.txt'
  )
  let _0x2d0ea8 = []
  try {
    const _0x4038e3 = await fs.readFile(_0x59e6f3, 'utf8')
    _0x2d0ea8 = _0x4038e3
      .split('\n')
      .filter((_0x5d3a5f) => _0x5d3a5f.trim() !== '')
    if (_0x2d0ea8.length === 0) {
      throw new Error("File keyword '" + _0x59e6f3 + "' kosong.")
    }
  } catch (_0x3b3c23) {
    throw new Error(
      "Gagal membaca file keyword '" + _0x59e6f3 + "': " + _0x3b3c23.message
    )
  }
  let _0x4f6e97 = -1
  try {
    const _0x4879d0 = await fs.readFile(LAST_KEYWORD_INDEX_FILE, 'utf8')
    _0x4f6e97 = parseInt(_0x4879d0.trim(), 10)
  } catch (_0x457b5c) {
    console.log(
      'File last_keyword_index.txt tidak ditemukan, memulai dari awal.'
    )
  }
  const _0x5ae7b8 = (_0x4f6e97 + 1) % _0x2d0ea8.length,
    _0x538416 = _0x2d0ea8[_0x5ae7b8]
  console.log(
    'Menggunakan keyword dari file (baris ' +
      (_0x5ae7b8 + 1) +
      '): "' +
      _0x538416 +
      '"'
  )
  const _0x5941ef = await getUsedItems(USED_PEXELS_FILE),
    _0x2b3921 = await getPexelsImage(_0x538416, _0x5941ef),
    _0x1f4e63 = await getGeminiContentForPexels(_0x538416),
    _0x572805 = config.settings.destinationLink
  await postToPinterest(_0x1ffc5d, {
    title: _0x1f4e63.title,
    description: _0x1f4e63.description,
    imageUrl: _0x2b3921.url,
    destinationLink: _0x572805,
  })
  await addUsedItem(USED_PEXELS_FILE, _0x2b3921.id)
  await fs.writeFile(LAST_KEYWORD_INDEX_FILE, String(_0x5ae7b8), 'utf8')
  console.log('--- \u2705 Mode Pexels Selesai ---')
}
async function createPinFromRss(_0x2a76e5) {
  console.log('\n--- \uD83D\uDE80 Memulai Mode RSS ---')
  const _0x4de4c5 = new RssParser({
      customFields: { item: ['media:content'] },
    }),
    _0x4aff8b = await _0x4de4c5.parseURL(config.rss.feedUrl),
    _0x4a5721 = await getUsedItems(USED_RSS_LINKS_FILE),
    _0x255849 = config.rss.startFromLink
  let _0x3ea4f0 = []
  const _0x2d939a = _0x4aff8b.items.map((_0x2cedc5) => _0x2cedc5.link),
    _0x37f3bc = _0x2d939a.indexOf(_0x255849)
  _0x255849 && _0x37f3bc !== -1
    ? (_0x3ea4f0 = _0x4aff8b.items.slice(0, _0x37f3bc))
    : (_0x3ea4f0 = _0x4aff8b.items)
  const _0x214c0a = _0x3ea4f0
    .filter((_0xeaae67) => !_0x4a5721.has(_0xeaae67.link))
    .sort(
      (_0x27f12b, _0x3fe8de) =>
        new Date(_0x27f12b.pubDate) - new Date(_0x3fe8de.pubDate)
    )
  if (_0x214c0a.length === 0) {
    console.log('Tidak ada artikel baru di RSS feed.')
    return
  }
  const _0x4b0ab6 = _0x214c0a[0]
  console.log('Menyiapkan artikel: "' + _0x4b0ab6.title + '"')
  let _0x2d5d03 = await findImageInRssEntry(_0x4b0ab6),
    _0x1dcc89 = null
  if (!_0x2d5d03) {
    const _0x398e51 = _0x4b0ab6.title.split(' '),
      _0x4990d3 =
        _0x398e51.find((_0x2e9263) => _0x2e9263.length > 4) || _0x398e51[0],
      _0x41b984 = await getUsedItems(USED_PEXELS_FILE),
      _0x46ba02 = await getPexelsImage(_0x4990d3, _0x41b984)
    _0x2d5d03 = _0x46ba02.url
    _0x1dcc89 = _0x46ba02.id
  }
  const _0x390769 = await getGeminiContentForRss(_0x4b0ab6.title)
  await postToPinterest(_0x2a76e5, {
    title: _0x4b0ab6.title,
    description: _0x390769,
    imageUrl: _0x2d5d03,
    destinationLink: _0x4b0ab6.link,
  })
  await addUsedItem(USED_RSS_LINKS_FILE, _0x4b0ab6.link)
  _0x1dcc89 && (await addUsedItem(USED_PEXELS_FILE, _0x1dcc89))
  console.log('--- \u2705 Mode RSS Selesai ---')
}
async function postToPinterest(_0x5a38ff, _0x4b5ada) {
  const _0x4eb69a = path.join(__dirname, 'temp_image.jpg')
  try {
    console.log('Mempersiapkan untuk memposting pin: "' + _0x4b5ada.title + '"')
    await _0x5a38ff.goto(config.pinterest.boardUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 90000,
    })
    console.log('Mencari tombol "Buat" (+) ...')
    await _0x5a38ff.waitForSelector(
      'button[aria-label="Create a Pin or add a Section"]',
      {
        visible: true,
        timeout: 30000,
      }
    )
    await _0x5a38ff.click('button[aria-label="Create a Pin or add a Section"]')
    console.log('Mencari menu "Pin"...')
    await _0x5a38ff.waitForSelector('button[data-test-id="Create Story Pin"]', {
      visible: true,
      timeout: 10000,
    })
    await Promise.all([
      _0x5a38ff.waitForNavigation({ waitUntil: 'domcontentloaded' }),
      _0x5a38ff.click('button[data-test-id="Create Story Pin"]'),
    ])
    console.log('Berhasil masuk ke Pin Builder. Mengunggah gambar...')
    const _0x3ab39b = await axios.get(_0x4b5ada.imageUrl, {
      responseType: 'arraybuffer',
      headers: {
        Referer: _0x4b5ada.destinationLink || config.pinterest.boardUrl,
      },
    })
    await fs.writeFile(_0x4eb69a, _0x3ab39b.data)
    const _0x40196d = await _0x5a38ff.waitForSelector('input[type=file]', {
      timeout: 60000,
    })
    await _0x40196d.uploadFile(_0x4eb69a)
    console.log('Mengisi detail Pin...')
    const _0x588cc6 =
        'div[data-test-id="editor-with-mentions"] [contenteditable="true"], div[data-test-id="pin-draft-description"] [contenteditable="true"]',
      _0x191b33 = 'input#WebsiteField, textarea[data-test-id="pin-draft-link"]',
      _0x1434e3 = 'input#storyboard-selector-interest-tags',
      _0x1ead55 =
        'div[data-test-id="storyboard-creation-nav-done"] button, button[data-test-id="board-dropdown-save-button"]'
    await _0x5a38ff.waitForSelector(
      'input#storyboard-selector-title, textarea[data-test-id="pin-draft-title"]',
      { timeout: 60000 }
    )
    await _0x5a38ff.type(
      'input#storyboard-selector-title, textarea[data-test-id="pin-draft-title"]',
      _0x4b5ada.title,
      { delay: 150 }
    )
    await _0x5a38ff.type(_0x588cc6, _0x4b5ada.description, { delay: 150 })
    _0x4b5ada.destinationLink &&
      (await _0x5a38ff.type(_0x191b33, _0x4b5ada.destinationLink, {
        delay: 150,
      }))
    const _0x56f52c = config.tags ? config.tags.count || 0 : 0
    if (_0x56f52c > 0) {
      try {
        const _0x503c27 = await _0x5a38ff.waitForSelector(_0x1434e3, {
          timeout: 5000,
        })
        let _0x4348c9 = 0
        const _0x3ba1e7 = await getGeminiTags(_0x4b5ada.title),
          _0x1bae57 = 'div[data-test-id="storyboard-suggestions-item"]'
        for (const _0x47fc5b of _0x3ba1e7) {
          if (_0x4348c9 >= _0x56f52c) {
            break
          }
          try {
            await _0x503c27.focus()
            await _0x5a38ff.type(_0x1434e3, _0x47fc5b, { delay: 150 })
            await _0x5a38ff.waitForTimeout(2000)
            await _0x5a38ff.waitForSelector(_0x1bae57, {
              visible: true,
              timeout: 5000,
            })
            await _0x5a38ff.click(_0x1bae57)
            _0x4348c9++
          } catch (_0x5e64ae) {
            await _0x503c27.focus()
            await _0x5a38ff.keyboard.down('Control')
            await _0x5a38ff.keyboard.press('A')
            await _0x5a38ff.keyboard.up('Control')
            await _0x5a38ff.keyboard.press('Backspace')
          }
        }
        console.log(
          'Total ' +
            _0x4348c9 +
            ' dari ' +
            _0x56f52c +
            ' tag berhasil ditambahkan.'
        )
      } catch (_0x5776a6) {
        console.log(
          'Kolom input tag tidak ditemukan, melewati langkah tagging.'
        )
      }
    }
    console.log('Mempublikasikan/Menyimpan Pin...')
    await _0x5a38ff.waitForSelector(_0x1ead55, { visible: true })
    await _0x5a38ff.click(_0x1ead55)
    await _0x5a38ff.waitForTimeout(15000)
    try {
      const _0x3ebb9e = await _0x5a38ff.waitForSelector(_0x1ead55, {
        timeout: 1000,
        visible: true,
      })
      _0x3ebb9e &&
        (console.log(
          'Tombol "Publish/Save" masih terlihat, mencoba klik sekali lagi...'
        ),
        await _0x3ebb9e.click(),
        await _0x5a38ff.waitForTimeout(10000))
    } catch (_0x493e0e) {
      console.log('Tombol "Publish/Save" sudah hilang, diasumsikan berhasil.')
    }
    console.log('\u2705 Pin "' + _0x4b5ada.title + '" berhasil diproses.')
  } finally {
    await fs.unlink(_0x4eb69a).catch(() => {})
  }
}
async function main() {
  await checkLicense()
  let _0x236e99
  try {
    if (process.env.PINTEREST_COOKIES) {
      await fs.writeFile(COOKIES_PATH, process.env.PINTEREST_COOKIES, 'utf8')
    } else {
      throw new Error("Secret 'PINTEREST_COOKIES' tidak ditemukan.")
    }
    const _0x13e903 = await fs.readFile(COOKIES_PATH, 'utf8')
    let _0xbd2b6e = JSON.parse(_0x13e903)
    const _0x1bd0dc = _0xbd2b6e.map((_0x1036c9) => {
      _0x1036c9.sameSite &&
        !['Lax', 'Strict', 'None'].includes(_0x1036c9.sameSite) &&
        delete _0x1036c9.sameSite
      for (const _0x3ad60a in _0x1036c9) {
        _0x1036c9[_0x3ad60a] === null && delete _0x1036c9[_0x3ad60a]
      }
      return _0x1036c9
    })
    _0x236e99 = await puppeteer.launch({
      executablePath: '/usr/bin/google-chrome',
      headless: 'new',
      args: ['--no-sandbox'],
    })
    const _0x43a059 = await _0x236e99.newPage()
    await _0x43a059.setUserAgent(
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36'
    )
    await _0x43a059.setCookie(..._0x1bd0dc)
    console.log('Cookies berhasil di-set ke browser.')
    ;(config.runMode === 'pexels' || config.runMode === 'both') &&
      (await createPinFromPexels(_0x43a059))
    ;(config.runMode === 'rss' || config.runMode === 'both') &&
      (await createPinFromRss(_0x43a059))
    console.log('\uD83C\uDF89 Semua tugas selesai.')
  } catch (_0x1d5c97) {
    console.error('Terjadi kesalahan fatal:', _0x1d5c97.message)
    if (_0x236e99) {
      const _0x139421 = (await _0x236e99.pages())[0],
        _0x1321fb = path.join(__dirname, 'error_screenshot.png')
      await _0x139421.screenshot({
        path: _0x1321fb,
        fullPage: true,
      })
      console.log('Screenshot error disimpan di: ' + _0x1321fb)
    }
    process.exit(1)
  } finally {
    _0x236e99 && (await _0x236e99.close())
  }
}
main()
