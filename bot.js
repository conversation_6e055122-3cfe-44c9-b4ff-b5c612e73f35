const puppeteer = require('puppeteer-extra'),
  StealthPlugin = require('puppeteer-extra-plugin-stealth'),
  axios = require('axios'),
  fs = require('fs').promises,
  path = require('path'),
  config = require('./config.json'),
  fetch = require('node-fetch')
puppeteer.use(StealthPlugin())
const VALIDATION_SERVER_URL =
    'https://license-server-botnews.vercel.app/api/validate',
  PRODUCT_NAME = 'pinterest'
async function checkLicense() {
  console.log('Memulai validasi lisensi...')
  const licenseEmail = process.env.BOT_LICENSE_EMAIL
  if (!licenseEmail) {
    throw new Error(
      "KESALAHAN: GitHub Secret 'BOT_LICENSE_EMAIL' tidak ditemukan."
    )
  }
  const validationUrl =
    VALIDATION_SERVER_URL +
    '?email=' +
    encodeURIComponent(licenseEmail) +
    '&product=' +
    PRODUCT_NAME
  try {
    const response = await fetch(validationUrl),
      responseData = await response.json()
    if (response.ok && responseData.status === 'valid') {
      return console.log('Lisensi valid.'), true
    }
    throw new Error(
      'Lisensi tidak valid (Status: ' +
        response.status +
        ') - ' +
        (responseData.message || 'Tidak ada pesan')
    )
  } catch (error) {
    throw new Error('Gagal menghubungi server lisensi: ' + error.message)
  }
}
const USED_PEXELS_FILE = path.join(__dirname, 'used_images.txt'),
  COOKIES_PATH = path.join(__dirname, 'cookies.json'),
  LAST_KEYWORD_INDEX_FILE = path.join(__dirname, 'last_keyword_index.txt')
async function getUsedItems(filePath) {
  try {
    const fileContent = await fs.readFile(filePath, 'utf8')
    return new Set(
      fileContent.split('\n').filter((line) => line.trim() !== '')
    )
  } catch (error) {
    if (error.code === 'ENOENT') {
      return new Set()
    }
    throw error
  }
}
async function addUsedItem(filePath, item) {
  await fs.appendFile(filePath, item + '\n')
}
async function getPexelsImage(keyword, usedImages) {
  console.log('Mencari gambar di Pexels dengan keyword: "' + keyword + '"')
  const pexelsResponse = await axios.get(
      'https://api.pexels.com/v1/search?query=' +
        encodeURIComponent(keyword) +
        '&per_page=20',
      { headers: { Authorization: process.env.PEXELS_API_KEY } }
    ),
    photos = pexelsResponse.data.photos
  if (!photos || photos.length === 0) {
    throw new Error(
      'Tidak ada gambar Pexels yang ditemukan untuk keyword: ' + keyword + '.'
    )
  }
  const availablePhoto = photos.find(
    (photo) => !usedImages.has(String(photo.id))
  )
  if (!availablePhoto) {
    throw new Error(
      'Semua gambar Pexels yang ditemukan sudah pernah digunakan.'
    )
  }
  return (
    console.log('Gambar Pexels ditemukan: ' + availablePhoto.url),
    {
      url: availablePhoto.src.large,
      id: String(availablePhoto.id),
    }
  )
}
async function getGeminiContentForPexels(keyword) {
  console.log('Menghubungi Gemini AI untuk konten...')
  const prompt = config.gemini.prompt.replace('{keyword}', keyword)
  try {
    const geminiResponse = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: prompt }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      generatedText = geminiResponse.data.candidates[0].content.parts[0].text,
      titleMatch = generatedText.match(/Judul: (.*)/),
      descriptionMatch = generatedText.match(/Deskripsi: (.*)/s)
    if (titleMatch && descriptionMatch) {
      return (
        console.log('Konten dari Gemini berhasil didapatkan.'),
        {
          title: titleMatch[1].trim(),
          description: descriptionMatch[1].trim(),
        }
      )
    }
  } catch (error) {
    console.error(
      'Error saat menghubungi Gemini AI:',
      error.response ? error.response.data : error.message
    )
  }
  return (
    console.log('Menggunakan konten fallback.'),
    {
      title: config.gemini.fallbackTitle,
      description: config.gemini.fallbackDescription.replace(
        '{keyword}',
        '#' + keyword.replace(/\s+/g, '')
      ),
    }
  )
}
async function getGeminiTags(pinTitle) {
  console.log('Menghubungi Gemini AI untuk kata pancingan tags...')
  const tagsPrompt = config.gemini.tagsPrompt.replace('{pinTitle}', pinTitle)
  try {
    const geminiResponse = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: tagsPrompt }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      generatedTags = geminiResponse.data.candidates[0].content.parts[0].text
    return generatedTags
      .split(',')
      .map((tag) => tag.trim())
      .filter((tag) => tag)
  } catch (error) {
    return (
      console.error(
        'Error saat membuat kata pancingan tags:',
        error.message
      ),
      []
    )
  }
}
async function main() {
  await checkLicense()
  let browser
  try {
    if (process.env.PINTEREST_COOKIES) {
      await fs.writeFile(COOKIES_PATH, process.env.PINTEREST_COOKIES, 'utf8')
    } else {
      throw new Error("Secret 'PINTEREST_COOKIES' tidak ditemukan.")
    }
    const cookiesContent = await fs.readFile(COOKIES_PATH, 'utf8')
    let cookies = JSON.parse(cookiesContent)
    const cleanedCookies = cookies.map((cookie) => {
      cookie.sameSite &&
        !['Lax', 'Strict', 'None'].includes(cookie.sameSite) &&
        delete cookie.sameSite
      for (const key in cookie) {
        cookie[key] === null && delete cookie[key]
      }
      return cookie
    })
    browser = await puppeteer.launch({
      executablePath: '/usr/bin/google-chrome',
      headless: 'new',
      args: ['--no-sandbox', '--window-size=1920,1080'],
    })
    const page = await browser.newPage()
    await page.setCookie(...cleanedCookies)
    console.log('Cookies berhasil di-set ke browser.')
    const keywordFilePath = path.join(
      __dirname,
      config.pexels.keywordFile || 'keywords.txt'
    )
    let keywords = []
    try {
      const keywordFileContent = await fs.readFile(keywordFilePath, 'utf8')
      keywords = keywordFileContent
        .split('\n')
        .filter((line) => line.trim() !== '')
      if (keywords.length === 0) {
        throw new Error("File keyword '" + keywordFilePath + "' kosong.")
      }
    } catch (error) {
      throw new Error(
        "Gagal membaca file keyword '" + keywordFilePath + "': " + error.message
      )
    }
    let lastKeywordIndex = -1
    try {
      const indexContent = await fs.readFile(LAST_KEYWORD_INDEX_FILE, 'utf8')
      lastKeywordIndex = parseInt(indexContent.trim(), 10)
    } catch (error) {
      console.log(
        'File last_keyword_index.txt tidak ditemukan, memulai dari awal.'
      )
    }
    const currentKeywordIndex = (lastKeywordIndex + 1) % keywords.length,
      currentKeyword = keywords[currentKeywordIndex]
    console.log(
      'Menggunakan keyword dari file (baris ' +
        (currentKeywordIndex + 1) +
        '): "' +
        currentKeyword +
        '"'
    )
    const usedImages = await getUsedItems(USED_PEXELS_FILE),
      selectedImage = await getPexelsImage(currentKeyword, usedImages),
      generatedContent = await getGeminiContentForPexels(currentKeyword),
      destinationLink = config.settings.destinationLink
    console.log('Navigasi ke halaman board: ' + config.pinterest.boardUrl)
    await page.goto(config.pinterest.boardUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 90000,
    })
    console.log('Mencari tombol "Buat" (+) ...')
    const createButtonSelector = 'button[aria-label="Create a Pin or add a Section"]'
    await page.waitForSelector(createButtonSelector, {
      visible: true,
      timeout: 30000,
    })
    await page.click(createButtonSelector)
    console.log('Mencari menu "Pin"...')
    const pinButtonSelector = 'button[data-test-id="Create Story Pin"]'
    await page.waitForSelector(pinButtonSelector, { visible: true })
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'domcontentloaded' }),
      page.click(pinButtonSelector),
    ])
    console.log('Berhasil masuk ke Pin Builder. Mengunggah gambar...')
    const imageResponse = await axios.get(selectedImage.url, {
        responseType: 'arraybuffer',
      }),
      tempImagePath = path.join(__dirname, 'temp_image.jpg')
    await fs.writeFile(tempImagePath, imageResponse.data)
    const fileInput = await page.waitForSelector('input[type=file]', {
      timeout: 60000,
    })
    await fileInput.uploadFile(tempImagePath)
    console.log('Mengisi detail Pin...')
    const titleSelector = 'input#storyboard-selector-title',
      descriptionSelector =
        'div[data-test-id="editor-with-mentions"] [contenteditable="true"]',
      websiteSelector = 'input#WebsiteField',
      tagsSelector = 'input#storyboard-selector-interest-tags'
    await page.waitForSelector(titleSelector, { timeout: 60000 })
    await page.type(titleSelector, generatedContent.title, { delay: 100 })
    await page.type(descriptionSelector, generatedContent.description, { delay: 100 })
    destinationLink && (await page.type(websiteSelector, destinationLink, { delay: 100 }))
    const maxTagsCount = config.tags.count || 3
    let addedTagsCount = 0
    const suggestedTags = await getGeminiTags(generatedContent.title)
    console.log('Kata pancingan tags dari AI: ' + suggestedTags.join(', '))
    const tagsInput = await page.waitForSelector(tagsSelector)
    for (const tag of suggestedTags) {
      if (addedTagsCount >= maxTagsCount) {
        break
      }
      try {
        console.log(
          'Mencoba mencari tag dengan kata pancingan: "' + tag + '"'
        )
        await tagsInput.focus()
        await page.type(tagsSelector, tag, { delay: 150 })
        await page.waitForTimeout(2000)
        await page.waitForSelector(
          'div[data-test-id="storyboard-suggestions-item"]',
          {
            visible: true,
            timeout: 5000,
          }
        )
        await page.click('div[data-test-id="storyboard-suggestions-item"]')
        addedTagsCount++
        console.log('Berhasil menambahkan tag.')
        await page.waitForTimeout(1500)
      } catch (error) {
        console.log(
          'Tidak ada saran tag untuk "' + tag + '", membersihkan input.'
        )
        await tagsInput.focus()
        await page.keyboard.down('Control')
        await page.keyboard.press('A')
        await page.keyboard.up('Control')
        await page.keyboard.press('Backspace')
        await page.waitForTimeout(500)
      }
    }
    console.log(
      'Total ' + addedTagsCount + ' dari ' + maxTagsCount + ' tag berhasil ditambahkan.'
    )
    console.log('Mempublikasikan Pin...')
    const publishButton = await page.waitForSelector(
      'div[data-test-id="storyboard-creation-nav-done"] button',
      { visible: true }
    )
    await publishButton.click()
    console.log('Menunggu 15 detik untuk proses publish...')
    await page.waitForTimeout(15000)
    const remainingPublishButton = await page['$'](
      'div[data-test-id="storyboard-creation-nav-done"] button'
    )
    remainingPublishButton
      ? (console.log(
          'Tombol "Publish" masih terlihat, mencoba klik sekali lagi...'
        ),
        await remainingPublishButton.click(),
        await page.waitForTimeout(10000))
      : console.log(
          'Tombol "Publish" sudah hilang, diasumsikan publish berhasil.'
        )
    console.log(
      '\u2705 Pin berhasil dibuat dengan judul: "' + generatedContent.title + '"'
    )
    await addUsedItem(USED_PEXELS_FILE, selectedImage.id)
    await fs.writeFile(LAST_KEYWORD_INDEX_FILE, String(currentKeywordIndex), 'utf8')
    console.log('\uD83C\uDF89 Tugas selesai.')
  } catch (error) {
    console.error('Terjadi kesalahan fatal:', error)
    if (browser) {
      const currentPage = (await browser.pages())[0],
        errorScreenshotPath = path.join(__dirname, 'error_screenshot.png')
      await currentPage.screenshot({
        path: errorScreenshotPath,
        fullPage: true,
      })
      console.log('Screenshot error disimpan di: ' + errorScreenshotPath)
    }
    process.exit(1)
  } finally {
    if (browser) {
      const tempImagePath = path.join(__dirname, 'temp_image.jpg')
      try {
        await fs.unlink(tempImagePath)
      } catch (error) {}
      await browser.close()
    }
  }
}
main()
