const puppeteer = require('puppeteer-extra'),
  StealthPlugin = require('puppeteer-extra-plugin-stealth'),
  axios = require('axios'),
  fs = require('fs').promises,
  path = require('path'),
  config = require('./config.json'),
  fetch = require('node-fetch')
puppeteer.use(StealthPlugin())
const VALIDATION_SERVER_URL =
    'https://license-server-botnews.vercel.app/api/validate',
  PRODUCT_NAME = 'pinterest'
async function checkLicense() {
  console.log('Memulai validasi lisensi...')
  const _0x10ce96 = process.env.BOT_LICENSE_EMAIL
  if (!_0x10ce96) {
    throw new Error(
      "KESALAHAN: GitHub Secret 'BOT_LICENSE_EMAIL' tidak ditemukan."
    )
  }
  const _0x284fc2 =
    VALIDATION_SERVER_URL +
    '?email=' +
    encodeURIComponent(_0x10ce96) +
    '&product=' +
    PRODUCT_NAME
  try {
    const _0x11376a = await fetch(_0x284fc2),
      _0x4b60f8 = await _0x11376a.json()
    if (_0x11376a.ok && _0x4b60f8.status === 'valid') {
      return console.log('Lisensi valid.'), true
    }
    throw new Error(
      'Lisensi tidak valid (Status: ' +
        _0x11376a.status +
        ') - ' +
        (_0x4b60f8.message || 'Tidak ada pesan')
    )
  } catch (_0x44ee7c) {
    throw new Error('Gagal menghubungi server lisensi: ' + _0x44ee7c.message)
  }
}
const USED_PEXELS_FILE = path.join(__dirname, 'used_images.txt'),
  COOKIES_PATH = path.join(__dirname, 'cookies.json'),
  LAST_KEYWORD_INDEX_FILE = path.join(__dirname, 'last_keyword_index.txt')
async function getUsedItems(_0x371d5f) {
  try {
    const _0x4050da = await fs.readFile(_0x371d5f, 'utf8')
    return new Set(
      _0x4050da.split('\n').filter((_0x2c2dae) => _0x2c2dae.trim() !== '')
    )
  } catch (_0x4607e3) {
    if (_0x4607e3.code === 'ENOENT') {
      return new Set()
    }
    throw _0x4607e3
  }
}
async function addUsedItem(_0x549d7d, _0x2fa466) {
  await fs.appendFile(_0x549d7d, _0x2fa466 + '\n')
}
async function getPexelsImage(_0x146e62, _0xb3e160) {
  console.log('Mencari gambar di Pexels dengan keyword: "' + _0x146e62 + '"')
  const _0x40ab74 = await axios.get(
      'https://api.pexels.com/v1/search?query=' +
        encodeURIComponent(_0x146e62) +
        '&per_page=20',
      { headers: { Authorization: process.env.PEXELS_API_KEY } }
    ),
    _0x381bcc = _0x40ab74.data.photos
  if (!_0x381bcc || _0x381bcc.length === 0) {
    throw new Error(
      'Tidak ada gambar Pexels yang ditemukan untuk keyword: ' + _0x146e62 + '.'
    )
  }
  const _0x469106 = _0x381bcc.find(
    (_0x23e0c3) => !_0xb3e160.has(String(_0x23e0c3.id))
  )
  if (!_0x469106) {
    throw new Error(
      'Semua gambar Pexels yang ditemukan sudah pernah digunakan.'
    )
  }
  return (
    console.log('Gambar Pexels ditemukan: ' + _0x469106.url),
    {
      url: _0x469106.src.large,
      id: String(_0x469106.id),
    }
  )
}
async function getGeminiContentForPexels(_0x4d9e1b) {
  console.log('Menghubungi Gemini AI untuk konten...')
  const _0x3eeeac = config.gemini.prompt.replace('{keyword}', _0x4d9e1b)
  try {
    const _0x11a995 = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: _0x3eeeac }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      _0x303e5e = _0x11a995.data.candidates[0].content.parts[0].text,
      _0x4fde09 = _0x303e5e.match(/Judul: (.*)/),
      _0x56a444 = _0x303e5e.match(/Deskripsi: (.*)/s)
    if (_0x4fde09 && _0x56a444) {
      return (
        console.log('Konten dari Gemini berhasil didapatkan.'),
        {
          title: _0x4fde09[1].trim(),
          description: _0x56a444[1].trim(),
        }
      )
    }
  } catch (_0x1b61be) {
    console.error(
      'Error saat menghubungi Gemini AI:',
      _0x1b61be.response ? _0x1b61be.response.data : _0x1b61be.message
    )
  }
  return (
    console.log('Menggunakan konten fallback.'),
    {
      title: config.gemini.fallbackTitle,
      description: config.gemini.fallbackDescription.replace(
        '{keyword}',
        '#' + _0x4d9e1b.replace(/\s+/g, '')
      ),
    }
  )
}
async function getGeminiTags(_0x2d0891) {
  console.log('Menghubungi Gemini AI untuk kata pancingan tags...')
  const _0x5884e6 = config.gemini.tagsPrompt.replace('{pinTitle}', _0x2d0891)
  try {
    const _0x4971a0 = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: _0x5884e6 }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      _0x5a4dd3 = _0x4971a0.data.candidates[0].content.parts[0].text
    return _0x5a4dd3
      .split(',')
      .map((_0x89e47a) => _0x89e47a.trim())
      .filter((_0xed5ef) => _0xed5ef)
  } catch (_0x55b9d0) {
    return (
      console.error(
        'Error saat membuat kata pancingan tags:',
        _0x55b9d0.message
      ),
      []
    )
  }
}
async function main() {
  await checkLicense()
  let _0x132348
  try {
    if (process.env.PINTEREST_COOKIES) {
      await fs.writeFile(COOKIES_PATH, process.env.PINTEREST_COOKIES, 'utf8')
    } else {
      throw new Error("Secret 'PINTEREST_COOKIES' tidak ditemukan.")
    }
    const _0x33af15 = await fs.readFile(COOKIES_PATH, 'utf8')
    let _0x206c45 = JSON.parse(_0x33af15)
    const _0x2bc4ca = _0x206c45.map((_0x38e7bd) => {
      _0x38e7bd.sameSite &&
        !['Lax', 'Strict', 'None'].includes(_0x38e7bd.sameSite) &&
        delete _0x38e7bd.sameSite
      for (const _0x13e533 in _0x38e7bd) {
        _0x38e7bd[_0x13e533] === null && delete _0x38e7bd[_0x13e533]
      }
      return _0x38e7bd
    })
    _0x132348 = await puppeteer.launch({
      executablePath: '/usr/bin/google-chrome',
      headless: 'new',
      args: ['--no-sandbox', '--window-size=1920,1080'],
    })
    const _0x3849b6 = await _0x132348.newPage()
    await _0x3849b6.setCookie(..._0x2bc4ca)
    console.log('Cookies berhasil di-set ke browser.')
    const _0x660b60 = path.join(
      __dirname,
      config.pexels.keywordFile || 'keywords.txt'
    )
    let _0x19662e = []
    try {
      const _0x10b6d1 = await fs.readFile(_0x660b60, 'utf8')
      _0x19662e = _0x10b6d1
        .split('\n')
        .filter((_0x5899ef) => _0x5899ef.trim() !== '')
      if (_0x19662e.length === 0) {
        throw new Error("File keyword '" + _0x660b60 + "' kosong.")
      }
    } catch (_0x3eb8a3) {
      throw new Error(
        "Gagal membaca file keyword '" + _0x660b60 + "': " + _0x3eb8a3.message
      )
    }
    let _0x3b76c8 = -1
    try {
      const _0x34f165 = await fs.readFile(LAST_KEYWORD_INDEX_FILE, 'utf8')
      _0x3b76c8 = parseInt(_0x34f165.trim(), 10)
    } catch (_0xa7ae5b) {
      console.log(
        'File last_keyword_index.txt tidak ditemukan, memulai dari awal.'
      )
    }
    const _0x44ff61 = (_0x3b76c8 + 1) % _0x19662e.length,
      _0x5ccf88 = _0x19662e[_0x44ff61]
    console.log(
      'Menggunakan keyword dari file (baris ' +
        (_0x44ff61 + 1) +
        '): "' +
        _0x5ccf88 +
        '"'
    )
    const _0x98456d = await getUsedItems(USED_PEXELS_FILE),
      _0x5942f8 = await getPexelsImage(_0x5ccf88, _0x98456d),
      _0x1e94af = await getGeminiContentForPexels(_0x5ccf88),
      _0x37d61f = config.settings.destinationLink
    console.log('Navigasi ke halaman board: ' + config.pinterest.boardUrl)
    await _0x3849b6.goto(config.pinterest.boardUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 90000,
    })
    console.log('Mencari tombol "Buat" (+) ...')
    const _0x5d0bed = 'button[aria-label="Create a Pin or add a Section"]'
    await _0x3849b6.waitForSelector(_0x5d0bed, {
      visible: true,
      timeout: 30000,
    })
    await _0x3849b6.click(_0x5d0bed)
    console.log('Mencari menu "Pin"...')
    const _0x487862 = 'button[data-test-id="Create Story Pin"]'
    await _0x3849b6.waitForSelector(_0x487862, { visible: true })
    await Promise.all([
      _0x3849b6.waitForNavigation({ waitUntil: 'domcontentloaded' }),
      _0x3849b6.click(_0x487862),
    ])
    console.log('Berhasil masuk ke Pin Builder. Mengunggah gambar...')
    const _0x7e7e97 = await axios.get(_0x5942f8.url, {
        responseType: 'arraybuffer',
      }),
      _0x4c0440 = path.join(__dirname, 'temp_image.jpg')
    await fs.writeFile(_0x4c0440, _0x7e7e97.data)
    const _0x37b065 = await _0x3849b6.waitForSelector('input[type=file]', {
      timeout: 60000,
    })
    await _0x37b065.uploadFile(_0x4c0440)
    console.log('Mengisi detail Pin...')
    const _0x4bdb4b = 'input#storyboard-selector-title',
      _0xcb85da =
        'div[data-test-id="editor-with-mentions"] [contenteditable="true"]',
      _0x4700ce = 'input#WebsiteField',
      _0x3575f3 = 'input#storyboard-selector-interest-tags'
    await _0x3849b6.waitForSelector(_0x4bdb4b, { timeout: 60000 })
    await _0x3849b6.type(_0x4bdb4b, _0x1e94af.title, { delay: 100 })
    await _0x3849b6.type(_0xcb85da, _0x1e94af.description, { delay: 100 })
    _0x37d61f && (await _0x3849b6.type(_0x4700ce, _0x37d61f, { delay: 100 }))
    const _0x4ecc40 = config.tags.count || 3
    let _0x44c399 = 0
    const _0x1bb570 = await getGeminiTags(_0x1e94af.title)
    console.log('Kata pancingan tags dari AI: ' + _0x1bb570.join(', '))
    const _0xf8a3d6 = await _0x3849b6.waitForSelector(_0x3575f3)
    for (const _0x353ac2 of _0x1bb570) {
      if (_0x44c399 >= _0x4ecc40) {
        break
      }
      try {
        console.log(
          'Mencoba mencari tag dengan kata pancingan: "' + _0x353ac2 + '"'
        )
        await _0xf8a3d6.focus()
        await _0x3849b6.type(_0x3575f3, _0x353ac2, { delay: 150 })
        await _0x3849b6.waitForTimeout(2000)
        await _0x3849b6.waitForSelector(
          'div[data-test-id="storyboard-suggestions-item"]',
          {
            visible: true,
            timeout: 5000,
          }
        )
        await _0x3849b6.click('div[data-test-id="storyboard-suggestions-item"]')
        _0x44c399++
        console.log('Berhasil menambahkan tag.')
        await _0x3849b6.waitForTimeout(1500)
      } catch (_0xe2a825) {
        console.log(
          'Tidak ada saran tag untuk "' + _0x353ac2 + '", membersihkan input.'
        )
        await _0xf8a3d6.focus()
        await _0x3849b6.keyboard.down('Control')
        await _0x3849b6.keyboard.press('A')
        await _0x3849b6.keyboard.up('Control')
        await _0x3849b6.keyboard.press('Backspace')
        await _0x3849b6.waitForTimeout(500)
      }
    }
    console.log(
      'Total ' + _0x44c399 + ' dari ' + _0x4ecc40 + ' tag berhasil ditambahkan.'
    )
    console.log('Mempublikasikan Pin...')
    const _0x3322fd = await _0x3849b6.waitForSelector(
      'div[data-test-id="storyboard-creation-nav-done"] button',
      { visible: true }
    )
    await _0x3322fd.click()
    console.log('Menunggu 15 detik untuk proses publish...')
    await _0x3849b6.waitForTimeout(15000)
    const _0x33be75 = await _0x3849b6['$'](
      'div[data-test-id="storyboard-creation-nav-done"] button'
    )
    _0x33be75
      ? (console.log(
          'Tombol "Publish" masih terlihat, mencoba klik sekali lagi...'
        ),
        await _0x33be75.click(),
        await _0x3849b6.waitForTimeout(10000))
      : console.log(
          'Tombol "Publish" sudah hilang, diasumsikan publish berhasil.'
        )
    console.log(
      '\u2705 Pin berhasil dibuat dengan judul: "' + _0x1e94af.title + '"'
    )
    await addUsedItem(USED_PEXELS_FILE, _0x5942f8.id)
    await fs.writeFile(LAST_KEYWORD_INDEX_FILE, String(_0x44ff61), 'utf8')
    console.log('\uD83C\uDF89 Tugas selesai.')
  } catch (_0xab4d3) {
    console.error('Terjadi kesalahan fatal:', _0xab4d3)
    if (_0x132348) {
      const _0x40655f = (await _0x132348.pages())[0],
        _0x2f82d4 = path.join(__dirname, 'error_screenshot.png')
      await _0x40655f.screenshot({
        path: _0x2f82d4,
        fullPage: true,
      })
      console.log('Screenshot error disimpan di: ' + _0x2f82d4)
    }
    process.exit(1)
  } finally {
    if (_0x132348) {
      const _0x1446b1 = path.join(__dirname, 'temp_image.jpg')
      try {
        await fs.unlink(_0x1446b1)
      } catch (_0x1a6e93) {}
      await _0x132348.close()
    }
  }
}
main()
