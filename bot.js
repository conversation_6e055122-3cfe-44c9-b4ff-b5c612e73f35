const puppeteer = require('puppeteer-extra'),
  StealthPlugin = require('puppeteer-extra-plugin-stealth'),
  axios = require('axios'),
  fs = require('fs').promises,
  path = require('path'),
  config = require('./config.json')
puppeteer.use(StealthPlugin())

const USED_PEXELS_FILE = path.join(__dirname, 'used_images.txt'),
  COOKIES_PATH = path.join(__dirname, 'cookies.json'),
  LAST_KEYWORD_INDEX_FILE = path.join(__dirname, 'last_keyword_index.txt')
async function getUsedItems(filePath) {
  try {
    const fileContent = await fs.readFile(filePath, 'utf8')
    return new Set(
      fileContent.split('\n').filter((line) => line.trim() !== '')
    )
  } catch (error) {
    if (error.code === 'ENOENT') {
      return new Set()
    }
    throw error
  }
}
async function addUsedItem(filePath, item) {
  await fs.appendFile(filePath, item + '\n')
}
async function getPexelsImage(keyword, usedImages) {
  console.log('Searching for Pexels image with keyword: "' + keyword + '"')
  const pexelsResponse = await axios.get(
      'https://api.pexels.com/v1/search?query=' +
        encodeURIComponent(keyword) +
        '&per_page=20',
      { headers: { Authorization: process.env.PEXELS_API_KEY } }
    ),
    photos = pexelsResponse.data.photos
  if (!photos || photos.length === 0) {
    throw new Error(
      'No Pexels images found for keyword: ' + keyword + '.'
    )
  }
  const availablePhoto = photos.find(
    (photo) => !usedImages.has(String(photo.id))
  )
  if (!availablePhoto) {
    throw new Error(
      'All found Pexels images have already been used.'
    )
  }
  return (
    console.log('Pexels image found: ' + availablePhoto.url),
    {
      url: availablePhoto.src.large,
      id: String(availablePhoto.id),
    }
  )
}
async function getGeminiContentForPexels(keyword) {
  console.log('Contacting Gemini AI for content...')
  const prompt = config.gemini.prompt.replace('{keyword}', keyword)
  try {
    const geminiResponse = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: prompt }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      generatedText = geminiResponse.data.candidates[0].content.parts[0].text,
      titleMatch = generatedText.match(/Judul: (.*)/),
      descriptionMatch = generatedText.match(/Deskripsi: (.*)/s)
    if (titleMatch && descriptionMatch) {
      return (
        console.log('Content from Gemini successfully obtained.'),
        {
          title: titleMatch[1].trim(),
          description: descriptionMatch[1].trim(),
        }
      )
    }
  } catch (error) {
    console.error(
      'Error contacting Gemini AI:',
      error.response ? error.response.data : error.message
    )
  }
  return (
    console.log('Using fallback content.'),
    {
      title: config.gemini.fallbackTitle,
      description: config.gemini.fallbackDescription.replace(
        '{keyword}',
        '#' + keyword.replace(/\s+/g, '')
      ),
    }
  )
}
async function getGeminiTags(pinTitle) {
  console.log('Contacting Gemini AI for tags...')
  const tagsPrompt = config.gemini.tagsPrompt.replace('{pinTitle}', pinTitle)
  try {
    const geminiResponse = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=' +
          process.env.GEMINI_API_KEY,
        { contents: [{ parts: [{ text: tagsPrompt }] }] },
        { headers: { 'Content-Type': 'application/json' } }
      ),
      generatedTags = geminiResponse.data.candidates[0].content.parts[0].text
    return generatedTags
      .split(',')
      .map((tag) => tag.trim())
      .filter((tag) => tag)
  } catch (error) {
    return (
      console.error(
        'Error generating tags:',
        error.message
      ),
      []
    )
  }
}
async function main() {
  let browser
  try {
    if (process.env.PINTEREST_COOKIES) {
      await fs.writeFile(COOKIES_PATH, process.env.PINTEREST_COOKIES, 'utf8')
    } else {
      throw new Error("Secret 'PINTEREST_COOKIES' not found.")
    }
    const cookiesContent = await fs.readFile(COOKIES_PATH, 'utf8')
    let cookies = JSON.parse(cookiesContent)
    const cleanedCookies = cookies.map((cookie) => {
      cookie.sameSite &&
        !['Lax', 'Strict', 'None'].includes(cookie.sameSite) &&
        delete cookie.sameSite
      for (const key in cookie) {
        cookie[key] === null && delete cookie[key]
      }
      return cookie
    })
    browser = await puppeteer.launch({
      executablePath: '/usr/bin/google-chrome',
      headless: 'new',
      args: ['--no-sandbox', '--window-size=1920,1080'],
    })
    const page = await browser.newPage()
    await page.setCookie(...cleanedCookies)
    console.log('Cookies successfully set to browser.')
    const keywordFilePath = path.join(
      __dirname,
      config.pexels.keywordFile || 'keywords.txt'
    )
    let keywords = []
    try {
      const keywordFileContent = await fs.readFile(keywordFilePath, 'utf8')
      keywords = keywordFileContent
        .split('\n')
        .filter((line) => line.trim() !== '')
      if (keywords.length === 0) {
        throw new Error("Keyword file '" + keywordFilePath + "' is empty.")
      }
    } catch (error) {
      throw new Error(
        "Failed to read keyword file '" + keywordFilePath + "': " + error.message
      )
    }
    let lastKeywordIndex = -1
    try {
      const indexContent = await fs.readFile(LAST_KEYWORD_INDEX_FILE, 'utf8')
      lastKeywordIndex = parseInt(indexContent.trim(), 10)
    } catch (error) {
      console.log(
        'File last_keyword_index.txt not found, starting from beginning.'
      )
    }
    const currentKeywordIndex = (lastKeywordIndex + 1) % keywords.length,
      currentKeyword = keywords[currentKeywordIndex]
    console.log(
      'Using keyword from file (line ' +
        (currentKeywordIndex + 1) +
        '): "' +
        currentKeyword +
        '"'
    )
    const usedImages = await getUsedItems(USED_PEXELS_FILE),
      selectedImage = await getPexelsImage(currentKeyword, usedImages),
      generatedContent = await getGeminiContentForPexels(currentKeyword),
      destinationLink = config.settings.destinationLink
    console.log('Navigating to board page: ' + config.pinterest.boardUrl)
    await page.goto(config.pinterest.boardUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 90000,
    })
    console.log('Looking for "Create" (+) button...')
    const createButtonSelector = 'button[aria-label="Create a Pin or add a Section"]'
    await page.waitForSelector(createButtonSelector, {
      visible: true,
      timeout: 30000,
    })
    await page.click(createButtonSelector)
    console.log('Looking for "Pin" menu...')
    const pinButtonSelector = 'button[data-test-id="Create Story Pin"]'
    await page.waitForSelector(pinButtonSelector, { visible: true })
    await Promise.all([
      page.waitForNavigation({ waitUntil: 'domcontentloaded' }),
      page.click(pinButtonSelector),
    ])
    console.log('Successfully entered Pin Builder. Uploading image...')
    const imageResponse = await axios.get(selectedImage.url, {
        responseType: 'arraybuffer',
      }),
      tempImagePath = path.join(__dirname, 'temp_image.jpg')
    await fs.writeFile(tempImagePath, imageResponse.data)
    const fileInput = await page.waitForSelector('input[type=file]', {
      timeout: 60000,
    })
    await fileInput.uploadFile(tempImagePath)
    console.log('Filling Pin details...')
    const titleSelector = 'input#storyboard-selector-title',
      descriptionSelector =
        'div[data-test-id="editor-with-mentions"] [contenteditable="true"]',
      websiteSelector = 'input#WebsiteField',
      tagsSelector = 'input#storyboard-selector-interest-tags'
    await page.waitForSelector(titleSelector, { timeout: 60000 })
    await page.type(titleSelector, generatedContent.title, { delay: 100 })
    await page.type(descriptionSelector, generatedContent.description, { delay: 100 })
    destinationLink && (await page.type(websiteSelector, destinationLink, { delay: 100 }))
    const maxTagsCount = config.tags.count || 3
    let addedTagsCount = 0
    const suggestedTags = await getGeminiTags(generatedContent.title)
    console.log('AI suggested tags: ' + suggestedTags.join(', '))
    const tagsInput = await page.waitForSelector(tagsSelector)
    for (const tag of suggestedTags) {
      if (addedTagsCount >= maxTagsCount) {
        break
      }
      try {
        console.log(
          'Trying to search for tag with keyword: "' + tag + '"'
        )
        await tagsInput.focus()
        await page.type(tagsSelector, tag, { delay: 150 })
        await page.waitForTimeout(2000)
        await page.waitForSelector(
          'div[data-test-id="storyboard-suggestions-item"]',
          {
            visible: true,
            timeout: 5000,
          }
        )
        await page.click('div[data-test-id="storyboard-suggestions-item"]')
        addedTagsCount++
        console.log('Successfully added tag.')
        await page.waitForTimeout(1500)
      } catch (error) {
        console.log(
          'No tag suggestions for "' + tag + '", clearing input.'
        )
        await tagsInput.focus()
        await page.keyboard.down('Control')
        await page.keyboard.press('A')
        await page.keyboard.up('Control')
        await page.keyboard.press('Backspace')
        await page.waitForTimeout(500)
      }
    }
    console.log(
      'Total ' + addedTagsCount + ' of ' + maxTagsCount + ' tags successfully added.'
    )
    console.log('Publishing Pin...')
    const publishButton = await page.waitForSelector(
      'div[data-test-id="storyboard-creation-nav-done"] button',
      { visible: true }
    )
    await publishButton.click()
    console.log('Waiting 15 seconds for publish process...')
    await page.waitForTimeout(15000)
    const remainingPublishButton = await page['$'](
      'div[data-test-id="storyboard-creation-nav-done"] button'
    )
    remainingPublishButton
      ? (console.log(
          '"Publish" button still visible, trying to click once more...'
        ),
        await remainingPublishButton.click(),
        await page.waitForTimeout(10000))
      : console.log(
          '"Publish" button has disappeared, assuming publish was successful.'
        )
    console.log(
      '\u2705 Pin successfully created with title: "' + generatedContent.title + '"'
    )
    await addUsedItem(USED_PEXELS_FILE, selectedImage.id)
    await fs.writeFile(LAST_KEYWORD_INDEX_FILE, String(currentKeywordIndex), 'utf8')
    console.log('\uD83C\uDF89 Task completed.')
  } catch (error) {
    console.error('Fatal error occurred:', error)
    if (browser) {
      const currentPage = (await browser.pages())[0],
        errorScreenshotPath = path.join(__dirname, 'error_screenshot.png')
      await currentPage.screenshot({
        path: errorScreenshotPath,
        fullPage: true,
      })
      console.log('Error screenshot saved at: ' + errorScreenshotPath)
    }
    process.exit(1)
  } finally {
    if (browser) {
      const tempImagePath = path.join(__dirname, 'temp_image.jpg')
      try {
        await fs.unlink(tempImagePath)
      } catch (error) {}
      await browser.close()
    }
  }
}
main()
